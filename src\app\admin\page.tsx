'use client'

import { useEffect, useState, useCallback } from 'react'
import Link from 'next/link'
import {
  FileText,
  HelpCircle,
  Upload,
  Users,
  TrendingUp,
  Plus,
  Eye,
  Edit,
  Calendar,
  User,
  Activity,
  BarChart3,
  ArrowUpRight,
  Clock
} from 'lucide-react'
import { useAuthContext } from '@/contexts/AuthContext'
import { createClient } from '@/lib/supabase-client'
import { formatDate, formatDateTime } from '@/lib/utils'
import { Post, HelpArticle, MediaFile, AnalyticsData } from '@/types'
import { Card, CardBody, CardHeader } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'

interface DashboardStats {
  totalPosts: number
  totalHelpArticles: number
  totalMediaFiles: number
  totalUsers: number
}

interface RecentActivity {
  id: string
  type: 'post' | 'help_article' | 'media_file'
  title: string
  action: 'created' | 'updated'
  author: string
  created_at: string
}

export default function AdminDashboard() {
  const { user, isAdmin } = useAuthContext()
  const [stats, setStats] = useState<DashboardStats>({
    totalPosts: 0,
    totalHelpArticles: 0,
    totalMediaFiles: 0,
    totalUsers: 0
  })
  const [recentPosts, setRecentPosts] = useState<Post[]>([])
  const [recentHelpArticles, setRecentHelpArticles] = useState<HelpArticle[]>([])
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true)

      // Fetch stats
      const [postsCount, helpCount, mediaCount, usersCount] = await Promise.all([
        supabase.from('posts').select('id', { count: 'exact', head: true }),
        supabase.from('help_articles').select('id', { count: 'exact', head: true }),
        supabase.from('media_files').select('id', { count: 'exact', head: true }),
        isAdmin() ? supabase.from('users').select('id', { count: 'exact', head: true }) : { count: 0 }
      ])

      setStats({
        totalPosts: postsCount.count || 0,
        totalHelpArticles: helpCount.count || 0,
        totalMediaFiles: mediaCount.count || 0,
        totalUsers: usersCount.count || 0
      })

      // Fetch recent posts
      const { data: posts } = await supabase
        .from('posts')
        .select(`
          *,
          author:users(name, email),
          category:categories(name)
        `)
        .order('created_at', { ascending: false })
        .limit(5)

      setRecentPosts(posts || [])

      // Fetch recent help articles
      const { data: helpArticles } = await supabase
        .from('help_articles')
        .select(`
          *,
          author:users(name, email),
          category:categories(name)
        `)
        .order('created_at', { ascending: false })
        .limit(5)

      setRecentHelpArticles(helpArticles || [])

    } catch (error: any) {
      console.error('Error fetching dashboard data:', error)
      // Display an error message to the user
      setErrorMessage('Failed to load dashboard data. Please try again later.');
    } finally {
      setLoading(false)
    }
  }, [isAdmin])

  useEffect(() => {
    fetchDashboardData()
  }, [fetchDashboardData])

  const statCards = [
    {
      name: 'Blog Posts',
      value: stats.totalPosts,
      icon: FileText,
      gradient: 'from-primary-500 to-primary-600',
      href: '/admin/posts',
      change: '+12%'
    },
    {
      name: 'Help Articles',
      value: stats.totalHelpArticles,
      icon: HelpCircle,
      gradient: 'from-accent-500 to-accent-600',
      href: '/admin/help',
      change: '+8%'
    },
    {
      name: 'Media Files',
      value: stats.totalMediaFiles,
      icon: Upload,
      gradient: 'from-secondary-500 to-secondary-600',
      href: '/admin/media',
      change: '+15%'
    },
    ...(isAdmin() ? [{
      name: 'Users',
      value: stats.totalUsers,
      icon: Users,
      gradient: 'from-warning-500 to-warning-600',
      href: '/admin/users',
      change: '+3%'
    }] : [])
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Skeleton Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="h-2.5 bg-gray-200 rounded-full w-24 mb-2"></div>
                  <div className="h-3.5 bg-gray-300 rounded-full w-16"></div>
                </div>
                <div className="p-3 rounded-xl bg-gray-100">
                  <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Skeleton Quick Actions */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <div className="h-5 bg-gray-200 rounded-full w-48 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center p-6 border-2 border-gray-100 rounded-xl">
                <div className="p-3 bg-gray-100 rounded-lg">
                  <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                </div>
                <div className="ml-4">
                  <div className="h-2.5 bg-gray-200 rounded-full w-32 mb-1"></div>
                  <div className="h-2 bg-gray-300 rounded-full w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Skeleton Recent Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
              <div className="h-5 bg-gray-200 rounded-full w-48 mb-6"></div>
              <div className="space-y-4">
                {[...Array(2)].map((_, j) => (
                  <div key={j} className="p-4 border border-gray-100 rounded-lg">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="h-2.5 bg-gray-200 rounded-full w-40 mb-2"></div>
                        <div className="flex items-center mt-2">
                          <div className="w-3 h-3 bg-gray-300 rounded-full mr-1"></div>
                          <div className="h-2 bg-gray-300 rounded-full w-20"></div>
                        </div>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
                        <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (errorMessage) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600">{errorMessage}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
        <CardBody className="p-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-neutral-900 mb-2">
                Welcome back, {user?.name || 'Admin'}! 👋
              </h1>
              <p className="text-neutral-600 text-lg">
                Here&apos;s a summary of your website&apos;s activity and performance.
              </p>
            </div>
            <div className="hidden lg:flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center">
                <BarChart3 className="w-8 h-8 text-white" />
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <Link
            key={stat.name}
            href={stat.href}
            className="group"
          >
            <Card hover className="h-full">
              <CardBody>
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-br ${stat.gradient} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex items-center text-success-600 text-sm font-medium">
                    <TrendingUp className="w-4 h-4 mr-1" />
                    {stat.change}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-neutral-600 mb-1">{stat.name}</p>
                  <p className="text-3xl font-bold text-neutral-900">{stat.value.toLocaleString()}</p>
                </div>
                <div className="mt-4 flex items-center text-neutral-500 group-hover:text-primary-600 transition-colors">
                  <span className="text-sm">View details</span>
                  <ArrowUpRight className="w-4 h-4 ml-1" />
                </div>
              </CardBody>
            </Card>
          </Link>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Link
            href="/admin/posts/create"
            className="group flex items-center p-6 border-2 border-gray-100 rounded-xl hover:border-blue-200 hover:bg-blue-50 transition-all duration-200"
          >
            <div className="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors duration-200">
              <Plus className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <span className="font-semibold text-gray-900 group-hover:text-blue-900">Create Blog Post</span>
              <p className="text-sm text-gray-500 mt-1">Write a new blog article</p>
            </div>
          </Link>
          <Link
            href="/admin/help/create"
            className="group flex items-center p-6 border-2 border-gray-100 rounded-xl hover:border-green-200 hover:bg-green-50 transition-all duration-200"
          >
            <div className="p-3 bg-green-100 rounded-lg group-hover:bg-green-200 transition-colors duration-200">
              <Plus className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <span className="font-semibold text-gray-900 group-hover:text-green-900">Create Help Article</span>
              <p className="text-sm text-gray-500 mt-1">Add documentation</p>
            </div>
          </Link>
          <Link
            href="/admin/media"
            className="group flex items-center p-6 border-2 border-gray-100 rounded-xl hover:border-purple-200 hover:bg-purple-50 transition-all duration-200"
          >
            <div className="p-3 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors duration-200">
              <Upload className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <span className="font-semibold text-gray-900 group-hover:text-purple-900">Upload Media</span>
              <p className="text-sm text-gray-500 mt-1">Manage files and images</p>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Posts */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Recent Blog Posts</h2>
            <Link
              href="/admin/posts"
              className="text-blue-600 hover:text-blue-700 text-sm font-semibold flex items-center"
            >
              View all
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
          <div className="space-y-4">
            {recentPosts.length > 0 ? (
              recentPosts.map((post) => (
                <div key={post.id} className="group p-4 border border-gray-100 rounded-lg hover:border-gray-200 hover:shadow-sm transition-all duration-200">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                        {post.title}
                      </h3>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <User className="w-3 h-3 mr-1" />
                        <span>{post.author?.name || post.author?.email}</span>
                        <span className="mx-2">•</span>
                        <Calendar className="w-3 h-3 mr-1" />
                        <span>{formatDate(post.created_at)}</span>
                      </div>
                      <div className="mt-3">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                          post.status === 'published'
                            ? 'bg-green-100 text-green-800'
                            : post.status === 'draft'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {post.status}
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <Link
                        href={`/blog/${post.slug}`}
                        className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                        title="View post"
                      >
                        <Eye className="w-4 h-4" />
                      </Link>
                      <Link
                        href={`/admin/posts/${post.id}/edit`}
                        className="p-1.5 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                        title="Edit post"
                      >
                        <Edit className="w-4 h-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <FileText className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">No blog posts yet.</p>
                <Link
                  href="/admin/posts/create"
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium mt-2 inline-block"
                >
                  Create your first post
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Recent Help Articles */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Recent Help Articles</h2>
            <Link
              href="/admin/help"
              className="text-blue-600 hover:text-blue-700 text-sm font-semibold flex items-center"
            >
              View all
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
          <div className="space-y-4">
            {recentHelpArticles.length > 0 ? (
              recentHelpArticles.map((article) => (
                <div key={article.id} className="group p-4 border border-gray-100 rounded-lg hover:border-gray-200 hover:shadow-sm transition-all duration-200">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-semibold text-gray-900 truncate group-hover:text-green-600 transition-colors">
                        {article.title}
                      </h3>
                      <div className="flex items-center mt-2 text-xs text-gray-500">
                        <User className="w-3 h-3 mr-1" />
                        <span>{article.author?.name || article.author?.email}</span>
                        <span className="mx-2">•</span>
                        <Calendar className="w-3 h-3 mr-1" />
                        <span>{formatDate(article.created_at)}</span>
                      </div>
                      <div className="mt-3">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                          article.status === 'published'
                            ? 'bg-green-100 text-green-800'
                            : article.status === 'draft'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {article.status}
                        </span>
                      </div>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <Link
                        href={`/help/${article.slug}`}
                        className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
                        title="View article"
                      >
                        <Eye className="w-4 h-4" />
                      </Link>
                      <Link
                        href={`/admin/help/${article.id}/edit`}
                        className="p-1.5 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded transition-colors"
                        title="Edit article"
                      >
                        <Edit className="w-4 h-4" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <HelpCircle className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 text-sm">No help articles yet.</p>
                <Link
                  href="/admin/help/create"
                  className="text-green-600 hover:text-green-700 text-sm font-medium mt-2 inline-block"
                >
                  Create your first article
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}