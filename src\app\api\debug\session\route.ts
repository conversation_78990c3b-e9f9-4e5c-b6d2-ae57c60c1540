import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session }, error } = await supabase.auth.getSession()

    console.log('Debug session check:')
    console.log('Session exists:', !!session)
    console.log('Session user:', session?.user?.email)
    console.log('Session error:', error)
    console.log('Cookies:', request.cookies.getAll())

    return NextResponse.json({
      hasSession: !!session,
      userEmail: session?.user?.email,
      userId: session?.user?.id,
      error: error?.message,
      cookies: request.cookies.getAll().map(c => ({ name: c.name, value: c.value.substring(0, 20) + '...' }))
    })
  } catch (err) {
    console.error('Debug session error:', err)
    return NextResponse.json({ error: 'Failed to check session' }, { status: 500 })
  }
}
