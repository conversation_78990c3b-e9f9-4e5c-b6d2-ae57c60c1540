'use client'

import { useEffect, useState, useCallback, useMemo, useRef } from 'react'
import { User as SupabaseUser } from '@supabase/supabase-js'
import { createClient } from '@/lib/supabase-client'
import { User } from '@/types'

interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null
  })

  const userCache = useRef<Record<string, User>>({});
  const supabase = createClient()

  const fetchUserData = useCallback(async (supabaseUser: SupabaseUser): Promise<User | null> => {
    if (userCache.current[supabaseUser.id]) {
      return userCache.current[supabaseUser.id];
    }

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', supabaseUser.id)
        .single()

      if (error) {
        // If user doesn't exist in our users table, create them
        if (error.code === 'PGRST116') {
          const newUser = {
            id: supabaseUser.id,
            email: supabaseUser.email!,
            name: supabaseUser.user_metadata?.name || null,
            avatar_url: supabaseUser.user_metadata?.avatar_url || null,
            role: 'viewer' as const
          }

          const { data: createdUser, error: createError } = await supabase
            .from('users')
            .insert([newUser])
            .select()
            .single()

          if (createError) throw createError
          if(createdUser) {
            userCache.current[supabaseUser.id] = createdUser;
          }
          return createdUser
        }
        throw error
      }
      if(data) {
        userCache.current[supabaseUser.id] = data;
      }
      return data
    } catch (err) {
      console.error('Error fetching user data:', err)
      return null
    }
  }, [])

  useEffect(() => {
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          const userData = await fetchUserData(session.user)
          setAuthState({ user: userData, loading: false, error: null })
        } else {
          setAuthState({ user: null, loading: false, error: null })
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [fetchUserData])

  const signIn = useCallback(async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }))
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const signUp = useCallback(async (email: string, password: string, name?: string) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name || ''
          }
        }
      })

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }))
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const signOut = useCallback(async () => {
    setAuthState(prev => ({ ...prev, loading: true }))
    
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }))
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }, [])

  const updateProfile = useCallback(async (updates: Partial<User>) => {
    if (!authState.user) return { success: false, error: 'Not authenticated' }

    setAuthState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', authState.user.id)
        .select()
        .single()

      if (error) {
        setAuthState(prev => ({ ...prev, loading: false, error: error.message }))
        return { success: false, error: error.message }
      }

      setAuthState(prev => ({ 
        ...prev, 
        user: data, 
        loading: false, 
        error: null 
      }))

      return { success: true, data }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred'
      setAuthState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return { success: false, error: errorMessage }
    }
  }, [authState.user])

  const hasRole = useCallback((role: User['role'] | User['role'][]): boolean => {
    if (!authState.user) return false
    
    if (Array.isArray(role)) {
      return role.includes(authState.user.role)
    }
    
    return authState.user.role === role
  }, [authState.user])

  const isAdmin = useCallback((): boolean => hasRole('admin'), [hasRole])
  const isEditor = useCallback((): boolean => hasRole(['admin', 'editor']), [hasRole])
  const canEdit = useCallback((): boolean => hasRole(['admin', 'editor']), [hasRole])

  return useMemo(() => ({
    ...authState,
    signIn,
    signUp,
    signOut,
    updateProfile,
    hasRole,
    isAdmin,
    isEditor,
    canEdit
  }), [authState, signIn, signUp, signOut, updateProfile, hasRole, isAdmin, isEditor, canEdit])
}